import React, { useState } from 'react';
// Import from the parent directory (simulating npm package import)
import { WebVRGame, WebVRGameConfig } from '../../src';

function App() {
  const [currentTest, setCurrentTest] = useState<'basic' | 'configured' | 'custom'>('basic');
  const [gameStats, setGameStats] = useState({
    score: 0,
    hits: 0,
    shots: 0,
    vrActive: false,
  });

  // Basic configuration test
  const basicConfig: WebVRGameConfig = {};

  // Configured test with custom settings
  const configuredConfig: WebVRGameConfig = {
    game: {
      targetCount: 5,
      scorePerHit: 15,
      bulletSpeed: 8,
    },
    visual: {
      backgroundColor: 0x001133,
      environment: 'city',
      scoreColor: 0x00ff00,
    },
    ui: {
      enterVRButtonText: '🎮 Start Game',
    },
    onScoreChange: (score) => {
      setGameStats(prev => ({ ...prev, score }));
    },
    onTargetHit: (_targetIndex, _points) => {
      setGameStats(prev => ({ ...prev, hits: prev.hits + 1 }));
    },
    onBulletFired: () => {
      setGameStats(prev => ({ ...prev, shots: prev.shots + 1 }));
    },
    onVREnter: () => {
      setGameStats(prev => ({ ...prev, vrActive: true }));
    },
    onVRExit: () => {
      setGameStats(prev => ({ ...prev, vrActive: false }));
    },
  };

  // Custom assets test (using default assets but different settings)
  const customConfig: WebVRGameConfig = {
    game: {
      targetCount: 2,
      scorePerHit: 50,
      bulletSpeed: 20,
      bulletTimeToLive: 1,
      targetRespawnTime: 500,
      targetPositionRange: {
        x: [-3, 3],
        y: [2, 4],
        z: [-8, -3],
      },
    },
    visual: {
      backgroundColor: 0x330011,
      environment: 'sunset',
      cameraFov: 90,
      scoreColor: 0xff6600,
      scoreFontSize: 0.8,
    },
    ui: {
      enterVRButtonText: '🚀 Launch VR',
      buttonStyle: {
        position: 'fixed',
        bottom: '30px',
        left: '50%',
        transform: 'translateX(-50%)',
        fontSize: '16px',
        padding: '10px 20px',
        backgroundColor: '#ff6600',
        color: 'white',
        border: 'none',
        borderRadius: '25px',
        cursor: 'pointer',
        boxShadow: '0 4px 8px rgba(0,0,0,0.3)',
      },
    },
    onScoreChange: (score) => {
      setGameStats(prev => ({ ...prev, score }));
    },
    onTargetHit: (targetIndex, points) => {
      setGameStats(prev => ({ ...prev, hits: prev.hits + 1 }));
    },
    onBulletFired: () => {
      setGameStats(prev => ({ ...prev, shots: prev.shots + 1 }));
    },
  };

  const getCurrentConfig = () => {
    switch (currentTest) {
      case 'basic': return basicConfig;
      case 'configured': return configuredConfig;
      case 'custom': return customConfig;
      default: return basicConfig;
    }
  };

  const resetStats = () => {
    setGameStats({
      score: 0,
      hits: 0,
      shots: 0,
      vrActive: gameStats.vrActive,
    });
  };

  return (
    <div style={{ width: '100vw', height: '100vh', position: 'relative' }}>
      {/* Test Controls */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        zIndex: 1000,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        color: 'white',
        padding: '15px',
        borderRadius: '8px',
        fontFamily: 'Arial, sans-serif',
        fontSize: '14px',
      }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#00ff88' }}>
          Integration Test
        </h3>
        
        <div style={{ marginBottom: '10px' }}>
          <label>Test Mode: </label>
          <select 
            value={currentTest} 
            onChange={(e) => setCurrentTest(e.target.value as 'basic' | 'configured' | 'custom')}
            style={{
              marginLeft: '5px',
              padding: '2px 5px',
              backgroundColor: '#333',
              color: 'white',
              border: '1px solid #555',
              borderRadius: '3px',
            }}
          >
            <option value="basic">Basic (Default)</option>
            <option value="configured">Configured</option>
            <option value="custom">Custom Style</option>
          </select>
        </div>

        <div style={{ fontSize: '12px', lineHeight: '1.4' }}>
          <div>Score: <strong>{gameStats.score}</strong></div>
          <div>Hits: <strong>{gameStats.hits}</strong></div>
          <div>Shots: <strong>{gameStats.shots}</strong></div>
          <div>Accuracy: <strong>
            {gameStats.shots > 0 ? ((gameStats.hits / gameStats.shots) * 100).toFixed(1) : '0'}%
          </strong></div>
          <div>VR: <strong>{gameStats.vrActive ? '🥽 Active' : '💻 Desktop'}</strong></div>
        </div>

        <button 
          onClick={resetStats}
          style={{
            marginTop: '8px',
            padding: '4px 8px',
            backgroundColor: '#cc3300',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
          }}
        >
          Reset Stats
        </button>
      </div>

      {/* Test Status */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 1000,
        backgroundColor: 'rgba(0, 100, 0, 0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontFamily: 'monospace',
        fontSize: '12px',
      }}>
        ✅ Component Loaded Successfully
        <br />
        Mode: {currentTest.toUpperCase()}
      </div>

      {/* WebVR Game Component */}
      <WebVRGame 
        key={currentTest} // Force re-render when switching tests
        config={getCurrentConfig()}
        style={{ 
          width: '100%', 
          height: '100%',
        }}
      />
    </div>
  );
}

export default App;
