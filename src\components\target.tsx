import { useGLTF } from "@react-three/drei";
import { useGlobalContext } from "../context/global-context";
import { useEffect, useMemo } from "react";
import { WebVRGameConfig } from "./WebVRGame";

type TargetProps = {
  targetIdx: number;
  config?: Required<WebVRGameConfig>;
};

export default function Target({ targetIdx, config: propConfig }: TargetProps) {
  const { targets, config: contextConfig } = useGlobalContext();
  const config = propConfig || contextConfig;
  const { scene } = useGLTF(config.assets.target);

  const target = useMemo(() => scene.clone(), [scene]);

  useEffect(() => {
    const range = config.game.targetPositionRange;
    target.position.set(
      Math.random() * (range.x[1] - range.x[0]) + range.x[0],
      Math.random() * (range.y[1] - range.y[0]) + range.y[0] + targetIdx * 0.5,
      Math.random() * (range.z[1] - range.z[0]) + range.z[0]
    );

    // Store target index in userData for hit detection
    target.userData = { targetIndex: targetIdx };

    targets.current.add(target);
  }, [targetIdx, config.game.targetPositionRange, target, targets]);

  return <primitive object={target} />;
}
