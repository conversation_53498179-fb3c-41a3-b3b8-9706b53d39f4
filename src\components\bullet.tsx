import { useGLTF } from "@react-three/drei";
import { Mesh, Vector3 } from "three";
import { BulletData } from "../types";
import { useRef } from "react";
import { useGlobalContext } from "../context/global-context";
import { useFrame } from "@react-three/fiber";

type BulletProps = {
  bulletData: BulletData;
};

const forwardVector = new Vector3(0, 0, -1);

export default function Bullet({ bulletData }: BulletProps) {
  const { removeBullet, targets, addScore, config } = useGlobalContext();
  const { scene } = useGLTF(config.assets.blaster);
  const bulletPrototype = scene.getObjectByName("bullet")! as Mesh;
  const ref = useRef<Mesh>(null);

  useFrame(() => {
    const now = performance.now();
    const bulletObject = ref.current!;
    const directionVector = forwardVector
      .clone()
      .applyQuaternion(bulletObject.quaternion);

    bulletObject.position.addVectors(
      bulletData.initPosition,
      directionVector.multiplyScalar(
        (config.game.bulletSpeed * (now - bulletData.timestamp)) / 1000
      )
    );

    [...targets.current]
      .filter((target) => target.visible)
      .forEach((target) => {
        const distance = target.position.distanceTo(bulletObject.position);

        if (distance < config.game.targetHitDistance) {
          removeBullet(bulletData.id);
          target.visible = false;
          addScore();

          // Get target index from userData if available
          const targetIndex = (target.userData as { targetIndex?: number })?.targetIndex || 0;
          config.onTargetHit?.(targetIndex, config.game.scorePerHit);

          setTimeout(() => {
            target.visible = true;
            const range = config.game.targetPositionRange;
            target.position.x = Math.random() * (range.x[1] - range.x[0]) + range.x[0];
            target.position.z = Math.random() * (range.z[1] - range.z[0]) + range.z[0];
          }, config.game.targetRespawnTime);
        }
      });
  });

  return (
    <mesh
      ref={ref}
      geometry={bulletPrototype.geometry}
      material={bulletPrototype.material}
      quaternion={bulletData.initQuaternion}
    ></mesh>
  );
}
