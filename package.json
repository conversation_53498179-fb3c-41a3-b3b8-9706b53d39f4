{"name": "webvr-game", "private": false, "version": "1.0.0", "type": "module", "description": "A configurable WebVR shooting game component built with React Three Fiber", "keywords": ["webvr", "react", "three.js", "vr", "game", "component"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "your-repository-url"}, "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./styles": "./dist/index.css"}, "files": ["dist", "public", "README.md", "LICENSE"], "scripts": {"dev": "vite", "build": "npm run build:lib && npm run build:app", "build:lib": "tsc && vite build --mode lib", "build:app": "tsc -b && vite build --mode app", "build:types": "tsc --emitDeclarationOnly --outDir dist", "lint": "eslint .", "preview": "vite preview", "deploy": "vercel --prod", "prepublishOnly": "npm run build:lib"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "three": ">=0.150.0"}, "dependencies": {"@react-three/drei": "^9.121.2", "@react-three/fiber": "^8.17.12", "@react-three/xr": "^6.4.12"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/three": "^0.172.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "react": "^18.3.1", "react-dom": "^18.3.1", "three": "^0.172.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vercel": "^39.3.0", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4"}}