import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'

  return {
    plugins: [
      react(),
      ...(isLib ? [dts({
        insertTypesEntry: true,
        include: ['src/**/*'],
        exclude: ['src/**/*.test.*', 'src/**/*.spec.*']
      })] : [])
    ],

    ...(isLib ? {
      // Library build configuration
      build: {
        lib: {
          entry: resolve(__dirname, 'src/index.ts'),
          name: 'WebVRGame',
          formats: ['es'],
          fileName: 'index'
        },
        rollupOptions: {
          external: [
            'react',
            'react-dom',
            'three',
            '@react-three/fiber',
            '@react-three/drei',
            '@react-three/xr'
          ],
          output: {
            globals: {
              react: 'React',
              'react-dom': 'ReactDOM',
              three: 'THREE'
            }
          }
        },
        sourcemap: true,
        emptyOutDir: true
      }
    } : {
      // App build configuration (default)
      build: {
        outDir: 'dist-app',
        emptyOutDir: true
      }
    }),

    // Common configuration
    assetsInclude: ['**/*.glb', '**/*.gltf', '**/*.ogg', '**/*.ttf'],

    // Development server configuration
    server: {
      port: 3000,
      open: true
    }
  }
})
