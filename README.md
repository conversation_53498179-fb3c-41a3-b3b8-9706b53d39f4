# WebVR Game Component

A configurable WebVR shooting game component built with React Three Fiber. This component can be easily integrated into any React application or used as a standalone VR experience.

## Features

- 🥽 **WebXR/WebVR Support** - Works with VR headsets and controllers
- 🎯 **Interactive Shooting Game** - Aim and shoot at targets in 3D space
- ⚙️ **Highly Configurable** - Customize assets, game settings, visuals, and UI
- 🎨 **Customizable Assets** - Use your own 3D models, sounds, and fonts
- 📱 **Responsive** - Works on desktop and mobile devices
- 🔧 **TypeScript Support** - Full type definitions included
- 🎮 **Event Callbacks** - Hook into game events for custom functionality

## Installation

### As a Component (Recommended)

```bash
npm install webvr-game
```

### Peer Dependencies

Make sure you have the required peer dependencies installed:

```bash
npm install react react-dom three
```

The component also requires these React Three Fiber dependencies:

```bash
npm install @react-three/fiber @react-three/drei @react-three/xr
```

## Quick Start

### Basic Usage

```tsx
import React from 'react';
import { WebVRGame } from 'webvr-game';

function App() {
  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <WebVRGame />
    </div>
  );
}

export default App;
```

### With Custom Configuration

```tsx
import React from 'react';
import { WebVRGame, WebVRGameConfig } from 'webvr-game';

const gameConfig: WebVRGameConfig = {
  game: {
    targetCount: 5,
    scorePerHit: 20,
    bulletSpeed: 15,
  },
  visual: {
    backgroundColor: 0x000033,
    environment: 'sunset',
  },
  ui: {
    enterVRButtonText: 'Start VR Game',
  },
  onScoreChange: (score) => {
    console.log('New score:', score);
  },
  onTargetHit: (targetIndex, points) => {
    console.log(`Hit target ${targetIndex} for ${points} points!`);
  },
};

function App() {
  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <WebVRGame config={gameConfig} />
    </div>
  );
}

export default App;
```

## Configuration Options

The `WebVRGameConfig` interface provides extensive customization options:

### Assets Configuration

```tsx
const config: WebVRGameConfig = {
  assets: {
    spaceStation: '/path/to/your/environment.glb',
    blaster: '/path/to/your/gun.glb',
    target: '/path/to/your/target.glb',
    font: '/path/to/your/font.ttf',
    sounds: {
      laser: '/path/to/laser-sound.ogg',
      score: '/path/to/score-sound.ogg',
    },
  },
};
```

### Game Settings

```tsx
const config: WebVRGameConfig = {
  game: {
    targetCount: 3,              // Number of targets
    bulletSpeed: 10,             // Speed of bullets
    bulletTimeToLive: 2,         // Bullet lifetime in seconds
    scorePerHit: 10,             // Points per target hit
    targetRespawnTime: 1000,     // Target respawn delay in ms
    targetHitDistance: 1,        // Hit detection distance
    targetPositionRange: {       // Target spawn area
      x: [-5, 5],
      y: [1, 5],
      z: [-10, -5],
    },
  },
};
```

### Visual Settings

```tsx
const config: WebVRGameConfig = {
  visual: {
    backgroundColor: 0x808080,           // Scene background color
    environment: 'warehouse',            // HDR environment preset
    cameraPosition: [0, 1.6, 2],        // Default camera position
    cameraFov: 75,                       // Camera field of view
    scoreColor: 0xffa276,                // Score text color
    scoreFontSize: 0.52,                 // Score text size
    scorePosition: [0, 0.67, -1.44],     // Score text position
  },
};
```

### UI Settings

```tsx
const config: WebVRGameConfig = {
  ui: {
    showEnterVRButton: true,
    enterVRButtonText: 'Enter VR',
    canvasStyle: {
      width: '100vw',
      height: '100vh',
      position: 'fixed',
    },
    buttonStyle: {
      position: 'fixed',
      bottom: '20px',
      left: '50%',
      transform: 'translateX(-50%)',
      fontSize: '20px',
    },
  },
};
```

### Event Callbacks

```tsx
const config: WebVRGameConfig = {
  onScoreChange: (score: number) => {
    // Called when score changes
    console.log('Score updated:', score);
  },
  onVREnter: () => {
    // Called when entering VR mode
    console.log('Entered VR');
  },
  onVRExit: () => {
    // Called when exiting VR mode
    console.log('Exited VR');
  },
  onTargetHit: (targetIndex: number, points: number) => {
    // Called when a target is hit
    console.log(`Target ${targetIndex} hit for ${points} points`);
  },
  onBulletFired: () => {
    // Called when a bullet is fired
    console.log('Bullet fired');
  },
};
```

## Advanced Usage

### Custom Styling

```tsx
import { WebVRGame } from 'webvr-game';
import 'webvr-game/styles'; // Import default styles

function App() {
  return (
    <WebVRGame
      className="my-vr-game"
      style={{
        border: '2px solid #333',
        borderRadius: '10px',
      }}
      config={{
        ui: {
          canvasStyle: {
            width: '800px',
            height: '600px',
            position: 'relative',
          },
        },
      }}
    />
  );
}
```

### Integration with State Management

```tsx
import React, { useState } from 'react';
import { WebVRGame } from 'webvr-game';

function GameWithStats() {
  const [score, setScore] = useState(0);
  const [hits, setHits] = useState(0);
  const [shots, setShots] = useState(0);

  return (
    <div>
      <div className="stats">
        <p>Score: {score}</p>
        <p>Hits: {hits}</p>
        <p>Shots: {shots}</p>
        <p>Accuracy: {shots > 0 ? ((hits / shots) * 100).toFixed(1) : 0}%</p>
      </div>

      <WebVRGame
        config={{
          onScoreChange: setScore,
          onTargetHit: () => setHits(prev => prev + 1),
          onBulletFired: () => setShots(prev => prev + 1),
        }}
      />
    </div>
  );
}
```

## Asset Requirements

### 3D Models
- **Space Station**: Environment model (GLB/GLTF format)
- **Blaster**: Gun model with a child object named "bullet" for bullet spawn point
- **Target**: Target model for shooting

### Audio Files
- **Laser Sound**: Sound effect for shooting (OGG format recommended)
- **Score Sound**: Sound effect for hitting targets (OGG format recommended)

### Fonts
- **Score Font**: TTF font file for score display

## Browser Compatibility

- **WebXR Support**: Modern browsers with WebXR support (Chrome, Edge, Firefox)
- **WebGL**: Required for 3D rendering
- **Audio Context**: Required for sound effects

## Development

### Running the Standalone App

```bash
npm install
npm run dev
```

### Building for Production

```bash
# Build as library
npm run build:lib

# Build as standalone app
npm run build:app

# Build both
npm run build
```

## Credits

This game's development would not have been possible without the help of the below project by [Bela](https://github.com/bbohlender) and [Felix](https://github.com/felixtrz):

https://github.com/meta-quest/webxr-first-steps-react

## License

MIT License - see LICENSE file for details.
