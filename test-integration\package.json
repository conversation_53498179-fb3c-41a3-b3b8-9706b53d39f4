{"name": "webvr-game-test-integration", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "three": "^0.172.0", "@react-three/fiber": "^8.17.12", "@react-three/drei": "^9.121.2", "@react-three/xr": "^6.4.12"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.3.5"}}