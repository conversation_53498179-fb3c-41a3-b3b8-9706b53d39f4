import { useGlobalContext } from "../context/global-context";
import Bullet from "./bullet";
import { WebVRGameConfig } from "./WebVRGame";

type BulletsProps = {
  config?: Required<WebVRGameConfig>;
};

export default function Bullets(_props: BulletsProps) {
  const { bullets } = useGlobalContext();
  return (
    <>
      {bullets.map((bulletData) => (
        <Bullet key={bulletData.id} bulletData={bulletData} />
      ))}
    </>
  );
}
