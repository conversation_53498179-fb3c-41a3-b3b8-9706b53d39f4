import { Canvas } from "@react-three/fiber";
import { GlobalProvider } from "../context/global-context";
import { Environment, Gltf, PerspectiveCamera } from "@react-three/drei";
import { createXRStore, XR } from "@react-three/xr";
import Gun from "./gun";
import Bullets from "./bullets";
import Target from "./target";
import Score from "./score";
import { CSSProperties } from "react";

export interface WebVRGameConfig {
  // Asset paths
  assets?: {
    spaceStation?: string;
    blaster?: string;
    target?: string;
    font?: string;
    sounds?: {
      laser?: string;
      score?: string;
    };
  };

  // Game settings
  game?: {
    targetCount?: number;
    bulletSpeed?: number;
    bulletTimeToLive?: number;
    scorePerHit?: number;
    targetRespawnTime?: number;
    targetHitDistance?: number;
    targetPositionRange?: {
      x: [number, number];
      y: [number, number];
      z: [number, number];
    };
  };

  // Visual settings
  visual?: {
    backgroundColor?: number;
    environment?: string;
    cameraPosition?: [number, number, number];
    cameraFov?: number;
    scoreColor?: number;
    scoreFontSize?: number;
    scorePosition?: [number, number, number];
  };

  // UI settings
  ui?: {
    showEnterVRButton?: boolean;
    enterVRButtonText?: string;
    canvasStyle?: CSSProperties;
    buttonStyle?: CSSProperties;
    overlayStyle?: CSSProperties;
  };

  // Event callbacks
  onScoreChange?: (score: number) => void;
  onVREnter?: () => void;
  onVRExit?: () => void;
  onTargetHit?: (targetIndex: number, score: number) => void;
  onBulletFired?: () => void;
}

const defaultConfig: Required<WebVRGameConfig> = {
  assets: {
    spaceStation: "/spacestation.glb",
    blaster: "/blaster.glb",
    target: "/target.glb",
    font: "/SpaceMono-Bold.ttf",
    sounds: {
      laser: "/laser.ogg",
      score: "/score.ogg",
    },
  },
  game: {
    targetCount: 3,
    bulletSpeed: 10,
    bulletTimeToLive: 2,
    scorePerHit: 10,
    targetRespawnTime: 1000,
    targetHitDistance: 1,
    targetPositionRange: {
      x: [-5, 5],
      y: [1, 5],
      z: [-10, -5],
    },
  },
  visual: {
    backgroundColor: 0x808080,
    environment: "warehouse",
    cameraPosition: [0, 1.6, 2],
    cameraFov: 75,
    scoreColor: 0xffa276,
    scoreFontSize: 0.52,
    scorePosition: [0, 0.67, -1.44],
  },
  ui: {
    showEnterVRButton: true,
    enterVRButtonText: "Enter VR",
    canvasStyle: {
      width: "100vw",
      height: "100vh",
      position: "fixed",
    },
    buttonStyle: {
      position: "fixed",
      bottom: "20px",
      left: "50%",
      transform: "translateX(-50%)",
      fontSize: "20px",
    },
    overlayStyle: {
      position: "fixed",
      display: "flex",
      width: "100vw",
      height: "100vh",
      flexDirection: "column",
      justifyContent: "space-between",
      alignItems: "center",
      color: "white",
      pointerEvents: "none",
    },
  },
  onScoreChange: () => {},
  onVREnter: () => {},
  onVRExit: () => {},
  onTargetHit: () => {},
  onBulletFired: () => {},
};

export interface WebVRGameProps {
  config?: WebVRGameConfig;
  className?: string;
  style?: CSSProperties;
}

export default function WebVRGame({ 
  config = {}, 
  className,
  style 
}: WebVRGameProps) {
  // Deep merge user config with defaults
  const mergedConfig = {
    ...defaultConfig,
    ...config,
    assets: {
      ...defaultConfig.assets,
      ...config.assets,
      sounds: { ...defaultConfig.assets.sounds, ...config.assets?.sounds }
    },
    game: {
      ...defaultConfig.game,
      ...config.game,
      targetPositionRange: {
        ...defaultConfig.game.targetPositionRange,
        ...config.game?.targetPositionRange
      }
    },
    visual: { ...defaultConfig.visual, ...config.visual },
    ui: { ...defaultConfig.ui, ...config.ui },
  };

  const xrStore = createXRStore({
    controller: {
      right: Gun,
    },
  });

  // Handle VR events
  xrStore.subscribe((state) => {
    if (state.isPresenting && mergedConfig.onVREnter) {
      mergedConfig.onVREnter();
    } else if (!state.isPresenting && mergedConfig.onVRExit) {
      mergedConfig.onVRExit();
    }
  });

  const targets = Array.from({ length: mergedConfig.game.targetCount }, (_, i) => (
    <Target key={i} targetIdx={i} config={mergedConfig} />
  ));

  return (
    <div className={className} style={style}>
      <GlobalProvider config={mergedConfig}>
        <>
          <Canvas style={mergedConfig.ui.canvasStyle}>
            <color args={[mergedConfig.visual.backgroundColor]} attach={"background"} />
            <PerspectiveCamera 
              makeDefault 
              position={mergedConfig.visual.cameraPosition} 
              fov={mergedConfig.visual.cameraFov} 
            />
            <Environment preset={mergedConfig.visual.environment as "warehouse" | "city" | "sunset" | "forest"} />
            <Gltf src={mergedConfig.assets.spaceStation} />
            <XR store={xrStore} />
            <Bullets config={mergedConfig} />
            {targets}
            <Score config={mergedConfig} />
          </Canvas>
          {mergedConfig.ui.showEnterVRButton && (
            <div style={mergedConfig.ui.overlayStyle}>
              <button
                style={{
                  ...mergedConfig.ui.buttonStyle,
                  pointerEvents: "auto",
                }}
                onClick={() => {
                  xrStore.enterVR();
                }}
              >
                {mergedConfig.ui.enterVRButtonText}
              </button>
            </div>
          )}
        </>
      </GlobalProvider>
    </div>
  );
}

// Export the component and types for external use
export { WebVRGame };
export type { WebVRGameConfig, WebVRGameProps };
