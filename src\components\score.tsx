import { PositionalAudio, Text } from "@react-three/drei";
import { useGlobalContext } from "../context/global-context";
import { useEffect, useRef } from "react";
import { PositionalAudio as PAudio } from "three";
import { WebVRGameConfig } from "./WebVRGame";

function formatScoreText(score: number) {
  return score.toString().padStart(4, "0");
}

type ScoreProps = {
  config?: Required<WebVRGameConfig>;
};

export default function Score({ config: propConfig }: ScoreProps) {
  const { score, config: contextConfig } = useGlobalContext();
  const config = propConfig || contextConfig;
  const soundRef = useRef<PAudio>(null);

  useEffect(() => {
    if (score > 0) {
      const scoreSound = soundRef.current!;
      if (scoreSound.isPlaying) scoreSound.stop();
      scoreSound.play();
    }
  }, [score]);
  return (
    <Text
      color={config.visual.scoreColor}
      font={config.assets.font}
      fontSize={config.visual.scoreFontSize}
      anchorX={"center"}
      anchorY={"middle"}
      position={config.visual.scorePosition}
      quaternion={[-0.4582265217274104, 0, 0, 0.8888354486549235]}
    >
      {formatScoreText(score)}
      <PositionalAudio ref={soundRef} url={config.assets.sounds.score} loop={false} />
    </Text>
  );
}
