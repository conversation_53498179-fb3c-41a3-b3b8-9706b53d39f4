#!/usr/bin/env node

/**
 * Test script to verify the WebVR Game component builds correctly
 * and can be imported as a library.
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${colors.blue}${colors.bold}Running: ${description}${colors.reset}`);
  log(`Command: ${command}`);
  
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    log(`${colors.green}✅ Success: ${description}${colors.reset}`);
    return { success: true, output };
  } catch (error) {
    log(`${colors.red}❌ Failed: ${description}${colors.reset}`);
    log(`Error: ${error.message}`);
    if (error.stdout) log(`Stdout: ${error.stdout}`);
    if (error.stderr) log(`Stderr: ${error.stderr}`);
    return { success: false, error };
  }
}

function checkFile(filePath, description) {
  if (existsSync(filePath)) {
    log(`${colors.green}✅ Found: ${description} (${filePath})${colors.reset}`);
    return true;
  } else {
    log(`${colors.red}❌ Missing: ${description} (${filePath})${colors.reset}`);
    return false;
  }
}

function checkPackageJson() {
  log(`\n${colors.blue}${colors.bold}Checking package.json configuration...${colors.reset}`);
  
  try {
    const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
    
    const checks = [
      { key: 'main', expected: 'dist/index.js' },
      { key: 'module', expected: 'dist/index.js' },
      { key: 'types', expected: 'dist/index.d.ts' },
    ];
    
    let allGood = true;
    checks.forEach(({ key, expected }) => {
      if (packageJson[key] === expected) {
        log(`${colors.green}✅ ${key}: ${packageJson[key]}${colors.reset}`);
      } else {
        log(`${colors.red}❌ ${key}: expected "${expected}", got "${packageJson[key]}"${colors.reset}`);
        allGood = false;
      }
    });
    
    // Check exports
    if (packageJson.exports && packageJson.exports['.']) {
      log(`${colors.green}✅ exports field configured${colors.reset}`);
    } else {
      log(`${colors.yellow}⚠️  exports field missing or incomplete${colors.reset}`);
    }
    
    // Check peerDependencies
    if (packageJson.peerDependencies) {
      log(`${colors.green}✅ peerDependencies configured${colors.reset}`);
    } else {
      log(`${colors.red}❌ peerDependencies missing${colors.reset}`);
      allGood = false;
    }
    
    return allGood;
  } catch (error) {
    log(`${colors.red}❌ Error reading package.json: ${error.message}${colors.reset}`);
    return false;
  }
}

async function main() {
  log(`${colors.bold}${colors.blue}🧪 WebVR Game Component Build Test${colors.reset}\n`);
  
  let allTestsPassed = true;
  
  // 1. Check package.json configuration
  if (!checkPackageJson()) {
    allTestsPassed = false;
  }
  
  // 2. Install dependencies
  const installResult = runCommand('npm install', 'Installing dependencies');
  if (!installResult.success) {
    allTestsPassed = false;
  }
  
  // 3. Run linting
  const lintResult = runCommand('npm run lint', 'Running ESLint');
  if (!lintResult.success) {
    log(`${colors.yellow}⚠️  Linting failed, but continuing...${colors.reset}`);
  }
  
  // 4. Build library
  const buildLibResult = runCommand('npm run build:lib', 'Building library');
  if (!buildLibResult.success) {
    allTestsPassed = false;
  }
  
  // 5. Check build outputs
  log(`\n${colors.blue}${colors.bold}Checking build outputs...${colors.reset}`);
  const requiredFiles = [
    { path: 'dist/index.js', desc: 'Main library file' },
    { path: 'dist/index.d.ts', desc: 'TypeScript declarations' },
  ];
  
  requiredFiles.forEach(({ path, desc }) => {
    if (!checkFile(path, desc)) {
      allTestsPassed = false;
    }
  });
  
  // 6. Check if main export can be imported (basic syntax check)
  if (existsSync('dist/index.js')) {
    try {
      const distContent = readFileSync('dist/index.js', 'utf8');
      if (distContent.includes('WebVRGame')) {
        log(`${colors.green}✅ WebVRGame export found in build${colors.reset}`);
      } else {
        log(`${colors.red}❌ WebVRGame export not found in build${colors.reset}`);
        allTestsPassed = false;
      }
    } catch (error) {
      log(`${colors.red}❌ Error reading build file: ${error.message}${colors.reset}`);
      allTestsPassed = false;
    }
  }
  
  // 7. Build standalone app
  const buildAppResult = runCommand('npm run build:app', 'Building standalone app');
  if (!buildAppResult.success) {
    log(`${colors.yellow}⚠️  App build failed, but library build is main concern${colors.reset}`);
  }
  
  // Final result
  log(`\n${colors.bold}${'='.repeat(50)}${colors.reset}`);
  if (allTestsPassed) {
    log(`${colors.green}${colors.bold}🎉 All tests passed! Component is ready for distribution.${colors.reset}`);
    log(`\n${colors.blue}Next steps:${colors.reset}`);
    log(`1. Test the component in the test-integration directory`);
    log(`2. Publish to npm: npm publish`);
    log(`3. Update documentation with any final changes`);
  } else {
    log(`${colors.red}${colors.bold}❌ Some tests failed. Please fix the issues above.${colors.reset}`);
    process.exit(1);
  }
  log(`${colors.bold}${'='.repeat(50)}${colors.reset}\n`);
}

main().catch(error => {
  log(`${colors.red}${colors.bold}Fatal error: ${error.message}${colors.reset}`);
  process.exit(1);
});
