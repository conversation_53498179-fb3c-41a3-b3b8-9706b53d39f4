// Main component exports
export { default as WebVRGame } from './components/WebVRGame';
export { WebVRGame as WebVRGameComponent } from './components/WebVRGame';

// Type exports
export type { 
  WebVRGameConfig, 
  WebVRGameProps 
} from './components/WebVRGame';

export type { BulletData } from './types';

// Context exports (for advanced usage)
export { 
  GlobalProvider, 
  useGlobalContext,
  bulletSpeed,
  bulletTimeToLive 
} from './context/global-context';

// Individual component exports (for advanced customization)
export { default as Gun } from './components/gun';
export { default as Bullets } from './components/bullets';
export { default as Bullet } from './components/bullet';
export { default as Target } from './components/target';
export { default as Score } from './components/score';

// CSS import (will be included in build)
import './index.css';
